// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import GameManager from "../GameManager";
import GameUtils from "./GameUtils";
import LocalUtils, { StringDictionary, TweenObject } from "../LocalUtils";
import GameStuffManager from "./GameStuffManager";
import Enemy from "../GameStuffAndComps/Enemy";
import Hero from "../GameStuffAndComps/Hero";
import NPC from "../GameStuffAndComps/NPC";
import { ResourceType } from "../GameStuffAndComps/Resource";
import StatueUnlockManager from "./StatueUnlockManager";

/**
 * 
 */

export type GameState = {
    value: boolean;
    allowChangeTimes: number;
    isOneSide: boolean;
    isSideToTrue?: boolean;
}

export default class GameDirector {

    public static get instance(): GameDirector {
        if(!this._instance) {
            this._instance = new GameDirector();
        }
        return this._instance;
    }
    private static _instance: GameDirector = null;

    protected updateCallbackId = -1;

    // constructor() {
    // }

    // get isCanPutInWoodStorage(): boolean {
    //     return GameUtils.rootGameWorld.nowAreaRef >= 5;
    // }
    
    /** 暂时无用 */
    gamePhase = 0;
    guideStep = 0;

    // isPoped = false;
    isReadyToWin = false;
    isGameOver = false;

    isHeroDeadGameOver = false;

    private _gameStates: StringDictionary<GameState>[];

    // 是否首次拥有足够的对应资源
    isFirstCoinEnough = false;
    isFirstWoodEnough = false;
    isFirstMeatEnough = false;
    isFirstFoodEnough = false;
    
    // 初次解锁，建议只更改一次
    isFirstTowerUnlock = false;
    isFirstHeroTowerUnlock = false;
    isFirstHeroUnlock = false;
    isFirstHeroGetWeapon = false;
    isFirstBuyerGetWeapon = false;

    isHeroFirstSkillRelease = false;
    
    isFirstNpcMeatCollectorCreated = false;
    isFirst3NpcMeatCollectorCreated = false;
    isFirstNpcLoggerCreated = false;
    isFirst3NpcLoggerCreated = false;
    isFirstNpcArcherCreated = false;

    // isFirstMeatTableNpcUnlock = false;
    // isFirstBucketUpgrade = false;

    isLastHeroSkill = false;

    // 解锁与否的状态
    isNpcUnlock = false;
    isConveyerUnlock = false;
    isConveyersUnlock: boolean[] = [false, false, false, false];
    isExpandAllowUnlocked = false;
    isExpand1Unlocked = false;
    isExpand2Unlocked = false;

    isTower2Unlock = false;

    isAllowUnlockStockade = false;

    // 控制开启大门
    isNPCComing = false;
    comingNPCNum = 0;

    // isBowMakerCreated = false;
    isEnemyFollowHero = false;

    isHeroPushingStripLog = false;
    isHeroPushingStripStone = false;

    isWorkerCreated = false;
    
    // isAllTargetFinished = false;
    // isAllFloorUnlockAreaUnlock = false;
    // isArriveGuidePoint = false;
    
    /** 河道中的栅栏是否全部解锁 */
    isAllWaterStockadeUnlock = false;

    // isWorkbenchEmpty = false;
    // nowMeatTableNpcNum = 1;
    // bucketLevel = 1;

    get isHeroPushing(): boolean {
        return this.isHeroPushingStripLog || this.isHeroPushingStripStone;
    }

    get isCanBuyFood(): boolean {
        return (this.isWorkerCreated && GameStuffManager.instance.worker.isCashierWorking) || GameUtils.rootGameWorld.FloorUIBuyFood.isAllow;
    }

    private _InitGameState() {
        this._gameStates = [];
        let state = this._gameStates['first_mainHero_shoot']
        this._gameStates['first_mainHero_shoot'] = {value: false, isOneSide: true, isSideToTrue: true, x: 1};
    }

    private _SetGameStateValue(state: GameState, value: boolean): boolean {
        if(state.isOneSide) {
            if(state.isSideToTrue && value == true || !state.isSideToTrue && value == false) {
                state.value = value;
                return true;
            }
        } else {
            state.value = value;
            return true;
        }
        return true;
    }

    /** 游戏状态
     * @ value: 状态值；
     * @ isOneSide: 控制该值是否只能单向更改；
     * @ isSideToTrue: 只能单向更改为 true / false，不能更改为另一个值
     * */
    GetGameState(propName: string): boolean {
        if(!this._gameStates) { this._InitGameState(); }
        return this._gameStates[propName].value;
    }

    /** 更改游戏状态为真，适用于仅改变一次的情况 */
    TrySetGameStateTrue(propName: string): number {
        return this.SetGameState(propName, true);
    }

    /** 更改游戏状态 */
    SetGameState(propName: string, value: boolean): number {
        if(!this._gameStates) { this._InitGameState(); }
        let state = this._gameStates[propName];
        let result = this._SetGameStateValue(state, value);
        if(result) {
            if(this._gameStates[propName] === value) {
                return 0;
            } else if(this._gameStates[propName] === value) {
                return 1;
            }
        } else {
            if(this._gameStates[propName] === value) {
                return 2;
            } else if(this._gameStates[propName] === value) {
                return 3;
            }
        }
        return 4;
    }

    GameStart() {
        // 初始化
        GameUtils.rootHeroControl.Init();
        GameStuffManager.instance.Init();
        StatueUnlockManager.instance.InitStatueUnlockUI();
        StatueUnlockManager.instance.InitFloorUnlockAreaUI();

        // 虚拟摇杆手指
        GameUtils.rootJoystickArea.ShowGuideFinger();

        // GameUtils.rootEnemyCreator.isCreatingBoss = true;
        // GameUtils.rootEnemyCreator.AllowCreateBoss();

        this.gamePhase = 1;
        this.guideStep = 1;
        GameUtils.rootEnemyCreator.StartCreate();
        // GameManager.instance.LateFrameCall(()=>{
        //     GameUtils.rootEnemyCreator.CreateFirstWave();
        // });

        // 地面 ui 初始解锁情况
        GameUtils.rootGameWorld.floorUnlockAreaList[0].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[1].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[2].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[3].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[4].SetCannotUnlock();

        GameUtils.rootGameWorld.floorUnlockAreaList[5].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[6].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[7].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[8].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[9].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[10].SetCannotUnlock();
        GameUtils.rootGameWorld.floorUnlockAreaList[11].SetCannotUnlock();

        // 开局资源
        // GameUtils.mainHeroBackpack.CreateResources(ResourceType.wood, 12);
        let resources = GameUtils.mainHeroBackpack.CreateResources(ResourceType.blockClod, 1);
        GameUtils.mainHero.script.isCarryingBlock = true;
        GameUtils.mainHero.script.carringBlock = resources[0];
        GameUtils.mainHero.script.unitStateMachine.PlayIdle();

        // 开局世界散落资源
        GameUtils.rootGameWorld.blockGoldStorageArea.CreateResources(ResourceType.blockGold, 1);

        // 开始导航
        // GameUtils.rootGuideToStatue.StartGuide();
    }

    SwitchGamePause() {
        if(this.isHeroDeadGameOver) { return true; }
        let isPause = GameManager.instance.isPause;
        if(!isPause) {
            GameManager.instance.GamePause();
            GameUtils.rootLayer_1.OnSwitchGamePause(true);
            return true;
        } else {
            GameManager.instance.GameResume();
            GameUtils.rootLayer_1.OnSwitchGamePause(false);
            return false;
        }
    }

    GameFrameStep() {
        GameManager.instance.GameStep();
    }

    HeroDeadGameOver() {
        if(this.isGameOver) return;
        this.isGameOver = true;
        GameManager.instance.GamePause();
        this.isHeroDeadGameOver = true;
        LocalUtils.CpSDKEnd(false);
        LocalUtils.CpSDKClick('end');
        GameManager.instance.scheduleOnce(()=>{
            // LocalUtils.CpSDKDownload();
            GameUtils.rootLayer_1.ShowEndPage();
        }, 1);
    }

    UpgradeTower(tower_id: number) {
        let tower = GameUtils.rootGameWorld.towers[tower_id - 1];
        if(tower) {
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.OpenHeroChoosePanelOnTower(tower_id - 1);
            }, 0.5);
        } else {
            console.error(`未找到塔！${tower_id}`);
        }
    }

    // UpgradeBucket() {
    //     console.log('升级铲斗！');
    //     this.bucketLevel += 1;
    //     let bucketScale = this.bucketLevel == 2 ? 0.8 : 1.1;
    //     GameUtils.rootGameWorldUI.PlayLevelUp(true);
    //     GameUtils.rootGameWorld.bucketFloorNode.scale = bucketScale;
    //     let rbPush = GameUtils.mainHero.script.unitNode.getChildByName('rbPush');
    //     rbPush.scale = bucketScale;
    // }

    // TowerHeroSkillRelease(tower_id: number) {
    //     let tower = GameUtils.rootGameWorld.towers[tower_id - 1];
    //     if(tower) {
    //         let hero = tower.GetTowerHero() as Hero;
    //         if(hero) {
    //             hero.TrySkill();
    //         }
    //     } else {
    //         console.error(`未找到塔！${tower_id}`);
    //     }
    // }

    OnBossDead() {
        if(this.isReadyToWin) {
            this.GameEnd();
        }
    }

    // OnAllTargetFinish() {
    //     if(!this.isAllTargetFinished) {
    //         this.isAllTargetFinished = true;
    //         GameManager.instance.LateTimeCallOnce(()=>{
    //             this.GameEnd();
    //         }, 1);
    //     }
    // }

    // OnCenterBuildingBeDestroyed() {
    //     if(this.isGameOver) return;
    //     this.isGameOver = true;
    //     GameManager.instance.GamePause();
    //     LocalUtils.CpSDKEnd(false);
    //     LocalUtils.CpSDKClick('end');
    //     GameManager.instance.scheduleOnce(()=>{
    //         // LocalUtils.CpSDKDownload();
    //         GameUtils.rootLayer_1.ShowEndPage();
    //     }, 1);
    // }

    OnHeroTPIn() {
        this.gamePhase = 1;
        this.guideStep = 1;
        // this.GuideToGatheringBuilding();
        GameUtils.rootGuideToStatue.GuideResetToStuff(null);
        
        // GameManager.instance.LateTimeCallOnce(()=>{
        //     for(let i = 0; i < 5; i++) {
        //         this.rootEnemyCreator.CreateABoss();
        //     }
        //     this.rootLayer_1.ShowBossComing();
        // }, 90);
        // GameManager.instance.LateTimeCallOnce(()=>{
        //     if(!this.isGameOver) {
        //         this.HeroDeadGameOver();
        //     }
        // }, 100);
    }

    OnFirstResourceNumEnough(resourceType: ResourceType) {
        console.log(`首次资源[${resourceType}]数量足够！`);
        if(resourceType == ResourceType.coin) {
            this.isFirstCoinEnough = true;
        } else if(resourceType == ResourceType.wood) {
            this.isFirstWoodEnough = true;
        } else if(resourceType == ResourceType.meat) {
            this.isFirstMeatEnough = true;
        } else if(resourceType == ResourceType.food) {
            this.isFirstFoodEnough = true;
            // 可以购买捞肉农民了
            GameUtils.rootGameWorld.floorUnlockAreaList[0].SetCanUnlock();
        }
    }

    OnNpcLoggerCreated() {
        if(!this.isFirstNpcLoggerCreated) {
            console.log(`首次购买伐木工！`);
            this.isFirstNpcLoggerCreated = true;
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.rootGameWorld.floorUnlockAreaList[2].SetCanUnlock();
            }, 0.4);
        }
        if(!this.isFirst3NpcLoggerCreated) {
            let npcLoggerNum = GameStuffManager.instance.npcLoggerList.length;
            if(npcLoggerNum >= 3) {
                console.log(`首次购买完3个伐木工！`);
                this.isFirst3NpcLoggerCreated = true;
            }
        }
    }
    
    OnNpcMeatCollectorCreated() {
        if(!this.isFirstNpcMeatCollectorCreated) {
            console.log(`首次购买捞肉农民！`);
            this.isFirstNpcMeatCollectorCreated = true;
            // GameUtils.rootEnemyCreator.StartCreate();
        }
        if(!this.isFirst3NpcMeatCollectorCreated) {
            let npcMeatCollectorNum =  GameStuffManager.instance.npcMeatCollectorList.length;
            if(npcMeatCollectorNum >= 3) {
                console.log(`首次购买完3个捞肉农民！`);
                this.isFirst3NpcMeatCollectorCreated = true;
                GameManager.instance.LateTimeCallOnce(()=>{
                    GameUtils.rootGameWorld.floorUnlockAreaList[10].SetCanUnlock();
                }, 0.4);
            }
        }
    }

    OnNpcArcherCreated() {
        if(!this.isFirstNpcArcherCreated) {
            console.log(`首次购买弓箭手！`);
            this.isFirstNpcArcherCreated = true;
            GameUtils.rootEnemyCreator.StartCreate();
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.rootGameWorld.floorUnlockAreaList[3].SetCanUnlock();
                GameUtils.rootGameWorld.floorUnlockAreaList[5].SetCanUnlock();
                GameUtils.rootGameWorld.floorUnlockAreaList[6].SetCanUnlock();
                // GameUtils.rootGameWorld.floorUnlockAreaList[8].SetCanUnlock();
                // GameUtils.rootGameWorld.floorUnlockAreaList[9].SetCanUnlock();
                GameUtils.rootLayer_1.ShowTargetUI();
            }, 1.5);
        }
    }

    OnNPCWorkerCreated() {
        this.isWorkerCreated = true;
        // this.isBowMakerCreated = true;
        // GameUtils.rootGameStuffs.isCreateBow = true;
        // GameUtils.rootGameStuffs.smoke_make.active = true;
        
        GameManager.instance.LateTimeCallOnce(()=>{
            GameUtils.rootGuideToStatue.isAutoGuide = false;
            GameUtils.rootGuideToStatue.GuideResetToStuff(GameUtils.rootGameWorld.bowStorageArea);
        }, 1.5);
    }

    // OnFirstWorkerCreated() {
    //     this.isFirstWorkerCreated = true;
    //     GameManager.instance.LateTimeCallOnce(()=>{
    //         GameUtils.rootGameWorld.floorUnlockAreaList[4].SetCanUnlock();
    //     }, 0.3);
    // }

    OnTowerUnlock(towerIndex: number) {
        if(!this.isFirstTowerUnlock) {
            this.isFirstTowerUnlock = true;
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.rootGameWorld.floorUnlockAreaList[11].SetCanUnlock();
                this.isAllowUnlockStockade = true;
            }, 0.5);
        }
        if(towerIndex >= 2 && towerIndex <= 5 &&!this.isFirstHeroTowerUnlock) {
            this.isFirstHeroTowerUnlock = true;
            // GameManager.instance.LateTimeCallOnce(()=>{
            //     GameUtils.rootGameWorld.floorUnlockAreaList[6].SetCanUnlock();
            //     GameUtils.rootGameWorld.floorUnlockAreaList[7].SetCanUnlock();
            // }, 1.5);
        }
        GameManager.instance.LateTimeCallOnce(()=>{
            // if(towerIndex == 0) {
            //     GameUtils.rootGameWorld.floorUnlockAreaList[1].SetCanUnlock();
            // } else if(towerIndex == 1) {
            //     GameUtils.rootGameWorld.floorUnlockAreaList[2].SetCanUnlock();
            // } else if(towerIndex == 2) {
            //     GameUtils.rootGameWorld.floorUnlockAreaList[3].SetCanUnlock();
            // } else if(towerIndex == 3) {
            //     GameUtils.rootGameWorld.floorUnlockAreaList[5].SetCanUnlock();
            // } else if(towerIndex == 5) {
            //     GameUtils.rootGameWorld.floorUnlockAreaList[4].SetCanUnlock();
            // } else if(towerIndex == 4) {
            //     GameUtils.rootGameWorld.floorUnlockAreaList[6].SetCanUnlock();
            //     GameUtils.rootGameWorld.floorUnlockAreaList[7].SetCanUnlock();
            // }
        }, 1.3);
    }

    OnHeroUnlock() {
        if(!this.isFirstHeroUnlock) {
            this.isFirstHeroUnlock = true;
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.rootGameWorld.floorUnlockAreaList[2].SetCanUnlock();
                // GameUtils.rootGameWorld.floorUnlockAreaList[3].SetCanUnlock();
                GameUtils.rootGameWorld.floorUnlockAreaList[4].SetCanUnlock();
            }, 0.5);
        }
    }

    OnHeroGatherd() {
        // this.guideStep = 2;
        // this.GuideToStockade();
    }

    // OnLogPushingCompleted() {
    //     if(!this.isFirstLogPushingCompleted) {
    //         this.isFirstLogPushingCompleted = true;
    //         // GameUtils.rootHeroControl.isCanHeroMove = false;
    //         GameUtils.rootGameWorldUI.resourcesPushGuideTexts[0].active = false;
    //     }
    // }
    // OnStonePushInPlace() {
    //     if(!this.isFirstStonePushingInPlace) {
    //         this.isFirstStonePushingInPlace = true;
    //         GameUtils.rootGameWorldUI.resourcesPushGuideTexts[1].active = true;
    //     }
    // }

    // OnStonePushCompleted() {
    //     if(!this.isFloorUnlockArea3Unlock) {
    //         this.isFloorUnlockArea3Unlock = true;
    //         GameUtils.rootGameWorld.floorUnlockAreaList[3].SetCanUnlock();
    //         GameUtils.rootGameWorldUI.resourcesPushGuideTexts[1].active = false;
    //     }
    // }

    OnStockadeUnlock() {
        // if(this.guideStep == 2) {
        //     this.guideStep = 3;
        //     GameUtils.rootGuideToStatue.GuideResetToStuff(null);
        // }
    }

    OnExpand1Unlock() {
        console.log('解锁扩展1！');
        this.isExpand1Unlocked = true;
    }

    OnExpand2Unlock() {
        console.log('解锁扩展2！');
        this.isExpand2Unlocked = true;
        GameManager.instance.LateTimeCallOnce(()=>{
            // 可以购买伐木工了
            GameUtils.rootGameWorld.floorUnlockAreaList[1].SetCanUnlock();
        }, 1.5);
    }

    // OnCenterBuildingUnlock() {
    //     console.log('解锁生产建筑！');
    //     this.gamePhase = 4;
    //     GameUtils.rootGameWorld.UnlockCenterBuilding();
    // }

    // OnConveyerUnlock(conveyerIndex: number) {
    //     console.log(`解锁传送带 ${conveyerIndex}！`);
    //     GameManager.instance.LateTimeCallOnce(()=>{
    //         GameUtils.rootGameWorld.UnlockConveyer(conveyerIndex);
    //         this.isConveyerUnlock = true;
    //         this.isConveyersUnlock[conveyerIndex] = true;
    //         if(this.isConveyersUnlock[0] && this.isConveyersUnlock[1] && !this.isExpandAllowUnlocked) {
    //             this.isExpandAllowUnlocked = true;
    //             this.OnConveyer1and2Unlock();
    //         }
    //         GameUtils.rootGameStuffs.isCreateWeapon[conveyerIndex] = true;
    //     }, 0.5);
    //     GameUtils.rootGameWorld.UnlockCenterBuilding(conveyerIndex + 1);
    // }

    // OnConveyer1and2Unlock() {
    //     console.log('解锁传送带1和2！');
    //     GameManager.instance.LateTimeCallOnce(()=>{
    //         GameUtils.rootGameWorld.floorUnlockAreaList[10].SetCanUnlock();
    //         GameUtils.rootGameWorld.floorUnlockAreaList[11].SetCanUnlock();
    //     }, 0.5);
    // }

    OnExpandUnlock(expandIndex: number) {
        console.log('解锁扩展！');
        // if(expandIndex == 0) {
        //     GameManager.instance.LateTimeCallOnce(()=>{
        //         GameUtils.rootGameWorld.floorUnlockAreaList[8].SetCanUnlock();
        //     }, 1.5);
        // } else if(expandIndex == 1) {
        //     GameManager.instance.LateTimeCallOnce(()=>{
        //         GameUtils.rootGameWorld.floorUnlockAreaList[9].SetCanUnlock();
        //     }, 1.5);
        // }
    }

    OnBedUnlock(index: number) {
        GameManager.instance.LateTimeCallOnce(()=>{
            if(index == 0) {
                GameUtils.rootGameWorld.floorUnlockAreaList[3].SetCanUnlock();
            } else if(index == 1) {
                GameUtils.rootGameWorld.floorUnlockAreaList[4].SetCanUnlock();
            } else if(index == 2) {
                GameUtils.rootGameWorld.floorUnlockAreaList[7].SetCanUnlock();
            }
        }, 0.5);
    }

    OnHeroSkillAfterAllFloorUnlock() {
        if(!this.isLastHeroSkill) {
            this.isLastHeroSkill = true;
            GameManager.instance.LateTimeCallOnce(()=>{
                this.GameEnd();
            }, 4);
        }
    }

    // OnUnlockArea(areaRef: number) {
    //     // console.log(`解锁区域${areaRef}！`);
    //     GameUtils.rootGameStuffs.ShowNodes(areaRef - 1);
    //     if(areaRef == 2) {
    //         let npc1 = GameUtils.rootNPCCreator.CreateANPCLogger(cc.v2(GameUtils.rootPathPoints.npcPathPoints[0].position));
    //         let npc2 = GameUtils.rootNPCCreator.CreateANPCLogger(cc.v2(GameUtils.rootPathPoints.npcPathPoints[1].position));
    //         (npc1.script as NPC).collectorPathPointRef = 0;
    //         (npc2.script as NPC).collectorPathPointRef = 1;
    //     } else if(areaRef == 3) {
    //         GameUtils.rootGameWorld.towers[0].OnUnlock();
    //     } else if(areaRef == 4) {
    //         GameManager.instance.LateTimeCallOnce(()=>{
    //             let resources = GameUtils.rootGameWorld.woodStorageArea.CreateResources(ResourceType.wood2, 3);
    //             resources.forEach((e)=>{
    //                 e.script.Flip(true);
    //             });
    //         }, 0.6);
    //     } else if(areaRef == 5) {
    //         GameUtils.rootGameWorld.statues[1].isCanUnlock = true;
    //         let ui = StatueUnlockManager.instance.statueUnlockProgressUIList[1];
    //         ui.node.active = true;
    //     } else if(areaRef == 6) {
    //         GameUtils.rootGameWorld.towers[1].OnUnlock();
    //     } else if(areaRef == 7) {
    //         GameUtils.rootGameWorld.statues[2].isCanUnlock = true;
    //         let ui = StatueUnlockManager.instance.statueUnlockProgressUIList[2];
    //         ui.node.active = true;
    //     } else if(areaRef == 8) {
    //         GameUtils.rootGameWorld.towers[2].OnUnlock();
            
    //         this.OnAllAreaUnlock();
    //     }
    // }

    // OnAllAreaUnlock() {
    //     console.log('全部区域解锁！');
    //     // GameManager.instance.LateTimeCallOnce(()=>{
    //     //     this.GameEnd();
    //     // }, 3);
    // }

    OnAllWaterStockadeUnlock() {
        console.log('全部水中栅栏解锁！');
        this.isAllWaterStockadeUnlock = true;
        GameUtils.rootGameWorld.stockades.forEach((e)=>{
            e.isCanUnlock = true;
        });
        GameManager.instance.LateTimeCallOnce(()=>{
            GameUtils.rootGameWorld.UnlockExpand1();
        }, 0.4);
    }

    // OnTower1HeroSkill() {
    //     if(this.isFirstTower1HeroSkill) return;
    //     this.isFirstTower1HeroSkill = true;
    //     // GameManager.instance.LateTimeCallOnce(()=>{
    //     //     this.CreateLeftMeat();
    //     // }, 2);
    //     // GameManager.instance.LateTimeCallOnce(()=>{
    //     //     // GameUtils.rootEnemyCreator.StartCreate();
    //     //     this.isAllowRawMeatDropCountinue = true;
    //     // }, 4);
    // }

    // private CreateLeftMeat() {
    //     let leftNum = 24 - this.rawMeatDropCount;
    //     if(leftNum <= 0) return;
    //     // GameStuffManager.instance.CreateDropMeat(cc.v2(-1000, 1000), leftNum);
    // }

    // OnFirstCoinEnough() {
    //     console.log('有足够的钱解锁区域了！');
    //     this.isFirstCoinEnough = true;
    //     GameUtils.rootGameWorld.floorUnlockAreaList[1].SetCanUnlock();
    //     GameUtils.rootGameWorld.floorUnlockAreaList[3].SetCanUnlock();
    // }

    // OnFirstUnlockFloorArea() {
    //     console.log('解锁了第一个区域！');
    //     this.isFirstUnlockFloorArea = true;
    //     GameManager.instance.LateTimeCallOnce(()=>{
    //         GameUtils.rootGameWorld.floorUnlockAreaList[0].SetCanUnlock();
    //         GameUtils.rootGameWorld.floorUnlockAreaList[2].SetCanUnlock();
    //     }, 1.5);
    // }

    // OnFirstMeatTableNpcUnlock() {
    //     this.isFirstMeatTableNpcUnlock = true;
    //     GameManager.instance.LateTimeCallOnce(()=>{
    //         GameUtils.rootGameWorld.floorUnlockAreaList[5].SetCanUnlock();
    //     }, 0.3);
    // }

    // OnFirstBucketUpgrade() {
    //     this.isFirstBucketUpgrade = true;
    // }

    // OnWorkbenchEmptyAndNotEnoughCoin() {
    //     if(!this.isWorkbenchEmpty) {
    //         // console.log('工作台空了！');
    //         this.isWorkbenchEmpty = true;
    //         let time = 0;
    //         GameManager.instance.AddGameUpdate('OnWorkbenchEmptyAndNotEnoughCoin', (dt: number)=>{
    //             if(!this.isWorkbenchEmpty) {
    //                 GameManager.instance.RemoveUpdateCallback();
    //             }
    //             time += dt;
    //             if(time > 0.3) {
    //                 GameUtils.rootGameWorldUI.resourcesPushGuideTexts[0].active = true;
    //                 this.isArriveGuidePoint = false;
    //                 GameManager.instance.RemoveUpdateCallback();
    //             }
    //         });
    //     }
    // }

    // OnWorkbenchNotEmpty() {
    //     if(this.isWorkbenchEmpty) {
    //         // console.log('工作台不空了！');
    //         this.isWorkbenchEmpty = false;
    //         GameUtils.rootGameWorldUI.resourcesPushGuideTexts[0].active = false;
    //     }
    // }

    // OnFirstBow() {
    //     if(!this.isFirstHeroGetWeapon) {
    //         this.isFirstHeroGetWeapon = true;
    //         console.log('英雄拿到弓！');
    //         GameUtils.rootGuideToStatue.isAutoGuide = false;
    //         GameUtils.rootGuideToStatue.GuideResetToStuff(GameUtils.rootGameWorld.bowStorageArea2);
    //     }
    // }

    OnSoldierGetWeapon() {
        if(this.gamePhase == 5) {
            this.gamePhase = 6;
            GameUtils.rootGuideToStatue.isAutoGuide = true;
        }
    }

    OnBuyerGetWeapon() {
        console.log('npc 拿到弓！');
        if(!this.isFirstBuyerGetWeapon) {
            GameUtils.rootGuideToStatue.isAutoGuide = true;
            this.isFirstBuyerGetWeapon = true;
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.rootGameWorld.floorUnlockAreaList[5].SetCanUnlock();
            }, 0.5);
        }
    }

    OnHeroSkillRelease() {
        if(!this.isHeroFirstSkillRelease) {
            this.isHeroFirstSkillRelease = true;
            let nearBoss: Enemy = null;
            // let bossDistance = -1;
            // GameStuffManager.instance.enemyList.forEach((e)=>{
            //     let enemy = e.script as Enemy;
            //     let distance = GameUtils.Fake3dDistanceExpand(enemy.rootPosition, GameUtils.rootGameWorld.towers[0].rootPosition);
            //     if(enemy.enemyRef == 6 && (bossDistance == -1 || distance < bossDistance)) {
            //         nearBoss = enemy;
            //         bossDistance = distance;
            //     }
            // });
            // if(nearBoss && nearBoss.isAlive) {
            // } else {
            //     console.log('没有找到boss！');
                nearBoss = GameUtils.rootEnemyCreator.GenerateEnemy(cc.v2(-1000, 500), 5).script as Enemy;
            // }
            nearBoss.isAllowJumpingSplitting = true;
            nearBoss.isCanNotGetHurt = true;
            nearBoss.body.scale = 1.3;
            GameManager.instance.LateFrameCall(()=>{
                GameUtils.mainHero.script.TurnFaceDir(false);
            });
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.rootCameraControl.ChengeFocus(nearBoss);
            }, 2);
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.rootGameWorldUI.resourcesPushGuideTexts[0].active = false;
                GameUtils.rootCameraControl.PlayCameraZoomScaleTo(1.6, 0.8);
            }, 2.5);
        }
    }

    OnBossJumpingSplitting() {
        GameManager.instance.LateTimeCallOnce(()=>{
            let tower = GameUtils.rootGameWorld.towers[0];
            let hero = GameUtils.rootGameWorld.towers[0].GetTowerHero();
            let heroSrcRootPosition = hero.rootPosition;
            let imgNode = tower.entityNode.getChildByName('img');
            let img_group = tower.entityNode.getChildByName('img_group');
            let anim = img_group.getComponent(cc.Animation);
            GameManager.instance.scheduleOnce(()=>{
                LocalUtils.PlaySound('Meteorite');
                imgNode.active = false;
                img_group.active = true;
            }, 0.04);
            anim.play('anim');
            LocalUtils.SetGlobalTimeScale(0.2);
            this.GameEnd(false);
            GameManager.instance.scheduleOnce(()=>{
                LocalUtils.SetGlobalTimeScale(0.5);
                GameManager.instance.scheduleOnce(()=>{
                    LocalUtils.SetGlobalTimeScale(1);
                }, 0.3);
            }, 0.3);
            cc.tween(new TweenObject(0, (value: number)=>{
                hero.rootPosition = heroSrcRootPosition.add(cc.v2(1000, 200).mul(value));
            })).delay(0.04).call(()=>{
                let bodyY = hero.body.y;
                hero.SetHeight(0);
                hero.body.y = bodyY;
            }).to(0.6, {value: 1}).start();
            let mainHero = GameUtils.mainHero.script;
            let mainHeroSrcRootPosition = mainHero.rootPosition;
            cc.tween(new TweenObject(0, (value: number)=>{
                mainHero.rootPosition = mainHeroSrcRootPosition.add(cc.v2(40, 0).mul(value));
            })).delay(0.04).call(()=>{
                mainHero.KillSelf();
            }).to(0.1, {value: 1}).start();
        }, 0.75);
    }

    OnAllFloorUnlockAreaUnlock() {
        console.log(`### 全部已解锁！`);
        // console.log(`### 全部已解锁，游戏即将结束！！`);

        // if(!this.isAllFloorUnlockAreaUnlock) {
        //     this.isAllFloorUnlockAreaUnlock = true;
        //     GameManager.instance.LateTimeCallOnce(()=>{
        //         this.GameEnd();
        //     }, 10);
        // }
    }

    OnAllStatuesAndFloorUnlockAreaUnlock() {
        console.log(`### 全部雕像和地面 UI 已解锁 ！！`);
        // GameUtils.rootGuideToStatue.statues[0].isCanUnlock = true;
        // GameUtils.rootGuideToStatue.PushAliveStatue(GameUtils.rootGuideToStatue.statues[0]);
    }

    OnNPCComing() {
        this.isNPCComing = true;
        this.comingNPCNum += 1;
    }

    OnNPCLeave() {
        this.comingNPCNum -= 1;
        if(this.comingNPCNum == 0) {
            this.isNPCComing = false;
        }
    }

    GuideToGatheringBuilding() {
        let target = GameUtils.rootGameWorld.gatheringBuildingList[0];
        let minDistance = target.rootPosition.sub(GameUtils.mainHero.script.rootPosition).len();
        GameUtils.rootGameWorld.gatheringBuildingList.forEach((e)=>{
            let distance = e.rootPosition.sub(GameUtils.mainHero.script.rootPosition).len();
            if(distance < minDistance) {
                target = e;
                minDistance = distance;
            }
        });
        GameUtils.rootGuideToStatue.GuideResetToStuff(target);
    }

    GuideToStockade() {
        let target = GameStuffManager.instance.FindAStockade(GameUtils.mainHero.script.rootPosition);
        if(target) {
            GameUtils.rootGuideToStatue.GuideResetToStuff(target);
        }
    }

    GameEnd(isWin: boolean = true) {
        this.isGameOver = true;
        LocalUtils.CpSDKEnd(isWin);
        LocalUtils.CpSDKClick('end');
        GameManager.instance.scheduleOnce(()=>{
            GameManager.instance.GamePause();
            // LocalUtils.CpSDKDownload();
            GameUtils.rootLayer_1.ShowEndPage(!isWin);
        }, 0.5);
    }


}