// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import Util from "../Base/Util";
import GameManager from "../GameManager";
import GameUtils from "./GameUtils";
import LocalUtils from "../LocalUtils";
import Building from "../GameStuffAndComps/Building";
import GameDirector from "./GameDirector";
import GameStuffManager from "./GameStuffManager";
import { ResourceType } from "../GameStuffAndComps/Resource";
import Stuff from "../GameStuffAndComps/Stuff";
import Tower from "../GameStuffAndComps/Tower";

const {ccclass, property} = cc._decorator;

@ccclass
export default class GameStuffs extends cc.Component {

    // @property(cc.Node)
    // smoke_make: cc.Node = null;
    // @property(cc.Node)
    // zhuozi_2: cc.Node = null;

    @property([cc.Node])
    newUnlockNodes_1: cc.Node[] = [];
    @property([cc.Node])
    newUnlockNodes_2: cc.Node[] = [];
    @property([cc.Node])
    newUnlockNodes_3: cc.Node[] = [];
    @property([cc.Node])
    newUnlockNodes_4: cc.Node[] = [];
    @property([cc.Node])
    newUnlockNodes_5: cc.Node[] = [];
    @property([cc.Node])
    newUnlockNodes_6: cc.Node[] = [];
    @property([cc.Node])
    newUnlockNodes_7: cc.Node[] = [];

    @property([cc.Node])
    floors: cc.Node[] = [];

    @property(cc.Node)
    building_board_1: cc.Node = null;
    @property(cc.Node)
    table_wood: cc.Node = null;

    // @property([cc.Node])
    // floor_guides: cc.Node[] = [];

    @property(cc.PhysicsPolygonCollider)
    trigger_area_stripLog: cc.PhysicsPolygonCollider = null;
    @property(cc.PhysicsPolygonCollider)
    trigger_area_stripStone: cc.PhysicsPolygonCollider = null;
    @property(cc.PhysicsPolygonCollider)
    trigger_area_groundMeat: cc.PhysicsPolygonCollider = null;
    @property(cc.PhysicsPolygonCollider)
    trigger_area_water: cc.PhysicsPolygonCollider = null;
    @property(cc.PolygonCollider)
    trigger_area_onWall: cc.PolygonCollider = null;
    @property(cc.PolygonCollider)
    trigger_area_inDigArea: cc.PolygonCollider = null;

    @property(Building)
    guidePoint: Building = null;

    private _isStripStoneCreatorRunning = false;
    private _stripStoneCreatorCreateTime = 0;
    private _stripStoneCreatorCreateTimeInterval = 8;

    private _recreateResourceTime = 0;

    private _createWeaponTime = 0;
    private _createWeaponTimeInterval = 4.5;

    isCreateWeapon: boolean[] = [false, false, false, false];
    isCreatingWeapon: boolean[] = [false, false, false, false];

    isTargetFinishedList: boolean[] = [false, false, false, false];

    private _driftingMeatNumLimit = 45;
    private _driftingMeatCreateTime = 1;
    private _driftingMeatCreateTimeInterval = 1;

    private _lastStockadeUnlockedNumViewNum = 0;
    private _lastNpcUnlockedNum = 0;
    private _lastBedUnlockedNum = 0;
    private _lastTowerUnlockedNum = 0;

    private _polygonForTrigger: cc.Vec2[][] = [];

    // LIFE-CYCLE CALLBACKS:
    onLoad () {
        GameUtils.rootGameStuffs = this;
    }

    start () {
        GameManager.instance.AddGameUpdate('GameStuffs', (dt: number)=>{
            this.gameUpdate(dt);
        });

        // this.CreateFirstWaveDriftMeat();
        // this.StartCreateStripStone();
    }

    gameUpdate(dt: number) {
        // this.TryStartCreateStripStone();
        // this.StripStoneCreatorUpdate(dt);
        // this.TryRecreateTrees();

        // this.TryRecreateDriftingMeat(dt);

        // this.RefreshFloorGuide();
        
        // if(this._recreateResourceTime < 1.2) {
        //     this._recreateResourceTime += dt;
        // } else if(this.TryRecreateResource()) {
        //     this._recreateResourceTime = 0;
        // }
        // let workbenchResourceNum = 0;
        // workbenchResourceNum += GameUtils.rootGameWorld.coinStorageArea.GetNumOfResource().resourceNums[ResourceType.coin];
        // workbenchResourceNum += GameUtils.rootGameWorld.meatStorageArea.GetNumOfResource().resourceNums[ResourceType.groundMeat];
        // workbenchResourceNum += GameUtils.rootGameWorld.cookStorageArea.GetNumOfResource().resourceNums[ResourceType.groundMeat];
        // workbenchResourceNum += GameUtils.rootGameWorld.cookStorageArea.GetNumOfResource().resourceNums[ResourceType.food];
        // workbenchResourceNum += GameUtils.rootGameWorld.buyFoodStorageArea.GetNumOfResource().resourceNums[ResourceType.food];
        // let minCost = 999999;
        // GameUtils.rootGameWorld.floorUnlockAreaList.forEach((e)=>{
        //     if(e.isCanUnlock && !e.isUnlockFinished) {
        //         if(e.leftCost < minCost) {
        //             minCost = e.leftCost;
        //         }
        //     }
        // });
        // if(workbenchResourceNum == 0 && GameUtils.GetResourceNum(ResourceType.coin) < minCost) {
        //     GameDirector.instance.OnWorkbenchEmptyAndNotEnoughCoin();
        // } else if(workbenchResourceNum > 0 || GameUtils.GetResourceNum(ResourceType.coin) >= minCost) {
        //     GameDirector.instance.OnWorkbenchNotEmpty();
        // }
        // if(!GameDirector.instance.isArriveGuidePoint) {
        //     let distance = GameUtils.mainHero.script.rootPosition.sub(this.guidePoint.rootPosition).len();
        //     if(distance < this.guidePoint.unlockDistance) {
        //         GameDirector.instance.isArriveGuidePoint = true;
        //     }
        // }
        // if(!GameDirector.instance.isFirstCoinEnough) {
        //     let coinNum = GameUtils.mainHeroBackpack.GetNumOfResource().resourceNums[ResourceType.coin];
        //     if(coinNum >= 20) {
        //         GameDirector.instance.OnFirstCoinEnough();
        //     }
        // }
        // if(this.isCreateWeapon.some((e)=>{ return e; })) { // 从铁匠铺造武器
        //     this._createWeaponTime += dt;
        //     // this.TryCreateWeaponResource();
        //     LocalUtils.ArrayForEach(this.isCreateWeapon, (e, i)=>{
        //         let timeCheackValue = this._createWeaponTimeInterval / this.isCreateWeapon.length * (i + 1);
        //         if(this._createWeaponTime > timeCheackValue && !this.isCreatingWeapon[i]) {
        //             if(this.TryCreateWeaponResource(i)) {
        //                 this.isCreatingWeapon[i] = true;
        //             }
        //         }
        //     }, (e, i)=>{ return e && !this.isCreatingWeapon[i]; });
        //     if(this._createWeaponTime > this._createWeaponTimeInterval) {
        //         this._createWeaponTime = 0;
        //         this.isCreatingWeapon.fill(false);
        //     }
        // }

        // if(!GameDirector.instance.isAllTargetFinished) { // 检查是否完成目标
        //     this.CheckTarget();
        // }
    }

    Init() {
        // this.HideAllNodes();
        // this.ShowNodes(1);
    }

    // 检查目标板中的目标是否完成
    // CheckTarget() {
    //     let stockadeAllNum = GameUtils.rootGameWorld.stockades.filter((e)=>{ return e.isStockadeActive}).length;
    //     let stockadeUnlockedNum = GameUtils.rootGameWorld.stockades.filter((e)=>{ return e.isStockadeActive && e.isStockadeUnlock }).length;
    //     let stockadeUnlockedNumViewNum = Math.floor(stockadeUnlockedNum/stockadeAllNum * 100);
    //     let npcUnlockedNum = GameStuffManager.instance.npcMeatCollectorList.length
    //           + GameStuffManager.instance.npcLoggerList.length
    //           + GameStuffManager.instance.npcArcherList.length;
    //     let bedUnlockedNum = GameStuffManager.instance.isBedUnlocked.filter((e)=>{return e;}).length;
    //     let towerUnlockedNum = GameStuffManager.instance.isTowerUnlocked.filter((e)=>{return e;}).length;
    //     this.isTargetFinishedList = [
    //         stockadeUnlockedNumViewNum > 80,
    //         npcUnlockedNum >= 8,
    //         bedUnlockedNum >= 3,
    //         towerUnlockedNum >= 3,
    //     ];
    //     if(stockadeUnlockedNumViewNum != this._lastStockadeUnlockedNumViewNum) {
    //         this._lastStockadeUnlockedNumViewNum = stockadeUnlockedNumViewNum;
    //         GameUtils.rootLayer_1.RefreshTargetUI(1, '' + stockadeUnlockedNumViewNum + '/80%', this.isTargetFinishedList[0]);
    //     }
    //     if(npcUnlockedNum != this._lastNpcUnlockedNum) {
    //         this._lastNpcUnlockedNum = npcUnlockedNum;
    //         GameUtils.rootLayer_1.RefreshTargetUI(2, '' + npcUnlockedNum + '/8', this.isTargetFinishedList[1]);
    //     }
    //     if(bedUnlockedNum != this._lastBedUnlockedNum) {
    //         this._lastBedUnlockedNum = bedUnlockedNum;
    //         GameUtils.rootLayer_1.RefreshTargetUI(3, '' + bedUnlockedNum + '/3', this.isTargetFinishedList[2]);
    //     }
    //     if(towerUnlockedNum != this._lastTowerUnlockedNum) {
    //         this._lastTowerUnlockedNum = towerUnlockedNum;
    //         GameUtils.rootLayer_1.RefreshTargetUI(4, '' + towerUnlockedNum + '/3', this.isTargetFinishedList[3]);
    //     }
    //     let isWin = this.isTargetFinishedList.every((e)=>{ return e; });
    //     if(isWin) {
    //         GameUtils.rootLayer_1.PlayTargetUIWin();
    //         GameDirector.instance.OnAllTargetFinish();
    //     }
    // }


    CreateFirstWaveDriftMeat() {
        let pos1 = cc.v2(GameUtils.rootPathPoints.driftingMeatPathPoints[0].position);
        let pos2 = cc.v2(GameUtils.rootPathPoints.driftingMeatPathPoints[1].position);
        for(let i = 0; i < 40; i++) {
            let pos = pos2.lerp(pos1, i / 30);
            pos = pos.add(cc.v2(1.4, 1).mul(-150 + Math.random() * 300));
            GameStuffManager.instance.CreateDriftingMeat(pos);
        }
    }

    TryRecreateDriftingMeat(dt: number) {
        let npcUnlockedNum = GameStuffManager.instance.npcMeatCollectorList.length
              + GameStuffManager.instance.npcLoggerList.length
              + GameStuffManager.instance.npcArcherList.length;
        let driftingMeatList = GameStuffManager.instance.driftingMeatList;
        let driftingMeatCreatePos = cc.v2(GameUtils.rootPathPoints.driftingMeatPathPoints[0].position);
        this._driftingMeatCreateTimeInterval = npcUnlockedNum > 6 ? 1.2 / (1 + (npcUnlockedNum - 6) * 0.3) : 1.2;
        if(driftingMeatList.length < this._driftingMeatNumLimit) {
            this._driftingMeatCreateTime -= dt;
            if(this._driftingMeatCreateTime < 0) {
                this._driftingMeatCreateTime = this._driftingMeatCreateTimeInterval * (0.2 + Math.random() * 0.8);
                let pos = driftingMeatCreatePos.add(cc.v2(1.4, 1).mul(-180 + Math.random() * 360));
                GameStuffManager.instance.CreateDriftingMeat(pos);
            }
        }
    }

    HideAllNodes() {
        this.HideNodes(1);
        this.HideNodes(2);
        this.HideNodes(3);
        this.HideNodes(4);
        this.HideNodes(5);
        this.HideNodes(6);
        this.HideNodes(7);
        this.floors[1].active = false;
        this.floors[2].active = false;
        this.floors[3].active = false;
        this.floors[4].active = false;
        this.floors[5].active = false;
        this.floors[6].active = false;
        this.floors[7].active = false;
    }

    HideNodes(nodesRef: number) {
        let nodes = [];
        switch(nodesRef) {
            case 1:
                nodes = this.newUnlockNodes_1;
                break;
            case 2:
                nodes = this.newUnlockNodes_2;
                break;
            case 3:
                nodes = this.newUnlockNodes_3;
                break;
            case 4:
                nodes = this.newUnlockNodes_4;
                break;
            case 5:
                nodes = this.newUnlockNodes_5;
                break;
            case 6:
                nodes = this.newUnlockNodes_6;
                break;
            case 7:
                nodes = this.newUnlockNodes_7;
                break;
            default:
                break;
        }
        nodes.forEach((e)=>{ e.active = false; });
    }

    ShowNodes(nodesRef: number) {
        let nodes = [];
        switch(nodesRef) {
            case 1:
                nodes = this.newUnlockNodes_1;
                break;
            case 2:
                nodes = this.newUnlockNodes_2;
                break;
            case 3:
                nodes = this.newUnlockNodes_3;
                break;
            case 4:
                nodes = this.newUnlockNodes_4;
                break;
            case 5:
                nodes = this.newUnlockNodes_5;
                break;
            case 6:
                nodes = this.newUnlockNodes_6;
                break;
            case 7:
                nodes = this.newUnlockNodes_7;
                break;
            default:
                break;
        }
        nodes.forEach((e)=>{
            let tower = e.getComponent(Tower);
            if(tower) {
                tower.Unlock();
            } else {
                let srcScaleX = e.scaleX;
                let srcScaleY = e.scaleY;
                e.scaleX = e.scaleX * 0.3;
                e.scaleY = e.scaleY * 0.3;
                cc.tween(e).to(0.3, {scaleX: srcScaleX, scaleY: srcScaleY}).start();
            }
            e.active = true;
        });
    }

    // TryStartCreateStripStone() {
    //     if(!this._isStripStoneCreatorRunning && GameDirector.instance.isConveyerUnlock) {
    //         this.StartCreateStripStone();
    //     }
    // }

    // StartCreateStripStone() {
    //     this._isStripStoneCreatorRunning = true;
    //     this._stripStoneCreatorCreateTime = 6;
    // }

    // StripStoneCreatorUpdate(dt: number) {
    //     if(!this._isStripStoneCreatorRunning) return;
    //     this._stripStoneCreatorCreateTime += dt;
    //     if(this._stripStoneCreatorCreateTime > this._stripStoneCreatorCreateTimeInterval) {
    //         this._stripStoneCreatorCreateTime = 0;
    //         GameUtils.rootGameWorld.CreatePushingStuff(cc.v2(GameUtils.rootPathPoints.stripStoneOutPathPoint.position), 2, true, true);
    //     }
    // }

    TryRecreateTrees() {
        let maxNumOfTrees = GameUtils.rootGameWorld.treeLocPoints.length;
        let numOfTrees = 0;
        GameUtils.rootGameWorld.isTreeLocPointUsed.forEach((e)=>{
            if(e) {
                numOfTrees += 1;
            }
        });
        if(numOfTrees < maxNumOfTrees - 4) {
            let randomIndex = Math.floor(Math.random() * maxNumOfTrees);
            if(GameUtils.rootGameWorld.isTreeLocPointUsed[randomIndex] || randomIndex == GameUtils.rootGameWorld.lastCutTreeLocPointIndex) {
                return;
            }
            let script = GameUtils.rootGameWorld.CreateATree(cc.v2(GameUtils.rootGameWorld.treeLocPoints[randomIndex].position));
            GameUtils.rootGameWorld.gatheringBuildingList.push(script);
            script.treeLocPointIndex = randomIndex;
            GameUtils.rootGameWorld.isTreeLocPointUsed[randomIndex] = true;
        }
    }

    // TryRecreateResource() {
    //     let nowMeatTableNpcNum = GameDirector.instance.nowMeatTableNpcNum;
    //     let resourceNum = GameStuffManager.instance.resourceLists[ResourceType.groundMeat].length;
    //     let timeDelay = 0.2;
    //     if(resourceNum < 50) {
    //         this.PlayMeatTableNpcAnim(0);
    //         GameManager.instance.LateTimeCallOnce(()=>{
    //             this.RecreateResource();
    //         }, timeDelay);
    //         for(let i = 1; i < nowMeatTableNpcNum; i++) {
    //             GameManager.instance.LateTimeCallOnce(()=>{
    //                 this.PlayMeatTableNpcAnim(i);
    //             }, i * 0.4);
    //             GameManager.instance.LateTimeCallOnce(()=>{
    //                 this.RecreateResource();
    //             }, timeDelay + i * 0.4);
    //         }
    //         return true;
    //     }
    //     return false;
    // }

    PlayMeatTableNpcAnim(npcIndex: number) {
        let meatTableNode = GameUtils.rootGameWorld.building_meatTable.node;
        let npcSpine = meatTableNode.getChildByName('npc_' + (npcIndex + 1)).getComponent(sp.Skeleton);
        let trackEntry: sp.spine.TrackEntry = npcSpine.setAnimation(0, 'dig', false);
        npcSpine.setTrackCompleteListener(trackEntry, ()=>{
            npcSpine.setAnimation(0, 'idle', true);
        });
    }

    RecreateResource() {
        let centerPos = cc.v2(-600, -200);
        let randomDistance = Math.random() * 350 + 220;
        let randomDir = LocalUtils.AngleToVec2(Util.RandomRange(-70, 30));
        let posOffset = randomDir.normalize().mul(randomDistance);
        let info = GameStuffManager.instance.CreateDropResource(centerPos, posOffset, ResourceType.groundMeat);
        info.script.MoveToGroundCanPush(0.6);
        GameStuffManager.instance.resourceLists[GameUtils.GetResourceIDByType(ResourceType.groundMeat)].push(info);
    }

    // RefreshFloorGuide() {
    //     if(!GameDirector.instance.isNpcUnlock && GameStuffManager.instance.mainStripLog && !GameStuffManager.instance.mainStripLog.isInPlace) {
    //         this.floor_guides[0].active = true;
    //     } else {
    //         this.floor_guides[0].active = false;
    //     }
    //     if(GameStuffManager.instance.mainStripStone && GameStuffManager.instance.mainStripStone.isBornComplited && !GameStuffManager.instance.mainStripStone.isInPlace) {
    //         this.floor_guides[1].active = true;
    //     } else {
    //         this.floor_guides[1].active = false;
    //     }
    // }

    TryCreateWeaponResource(createIndex: number) {
        let centerBuilding: Building = GameUtils.rootGameWorld.centerBuildings[createIndex + 1];
        let createPos = centerBuilding.rootPosition;
        if(createIndex == 0 || createIndex == 2) {
            // console.log('生产弓！');
            GameStuffManager.instance.CreateConveyerResource(ResourceType.bow, createPos, true, createIndex);
            return true;
        } else if(createIndex == 1 || createIndex == 3) {
            GameStuffManager.instance.CreateConveyerResource(ResourceType.sword, createPos, true, createIndex);
            return true;
        }
        return false;
    }

    /** 检测是否在触发器范围内
     * 支持矩形碰撞和多边形碰撞
     */
    JudgeIsInTriggerArea(gameWorldPos: cc.Vec2, ref: number = -1): boolean {
        let trigger: cc.Collider;
        if(ref == 0) {
            trigger = this.trigger_area_onWall;
        } else if(ref == 1) {
            trigger = this.trigger_area_inDigArea;
        }
        let triggerNodeInGameWorldPos = GameUtils.rootGameWorld.MiddleNode.convertToNodeSpaceAR(trigger.node.convertToWorldSpaceAR(cc.v2()));
        
        let polygon: cc.Vec2[] = [];
        if(this._polygonForTrigger[ref]) {
            polygon = this._polygonForTrigger[ref];
        } else if(trigger instanceof cc.BoxCollider) {
            let triggerPos = triggerNodeInGameWorldPos;
            let size = trigger.size;
            let center = triggerPos.add(trigger.offset);
            let rect = cc.rect(center.x - size.width / 2, center.y - size.height / 2, size.width, size.height);
            polygon = [
                cc.v2(rect.xMin, rect.yMin),
                cc.v2(rect.xMax, rect.yMin),
                cc.v2(rect.xMax, rect.yMax),
                cc.v2(rect.xMin, rect.yMax),
            ];
            this._polygonForTrigger[ref] = polygon;
        } else if(trigger instanceof cc.PolygonCollider) {
            let triggerPos = triggerNodeInGameWorldPos;
            let triggerPoints: cc.Vec2[] = trigger.points;
            let offset = trigger.offset;
            let center = triggerPos.add(offset);
            let polygon: cc.Vec2[] = [];
            triggerPoints.forEach((e, i)=>{
                polygon[i] = center.add(e);
            });
            this._polygonForTrigger[ref] = polygon;
        } else {
            console.warn('触发器范围类型未知！');
        }
        if(polygon && polygon.length > 0) {
            return cc.Intersection.pointInPolygon(gameWorldPos, polygon);
        }
    }
}
