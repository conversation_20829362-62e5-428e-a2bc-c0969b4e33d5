// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import Util from "../Base/Util";
import ColorMixer from "../ColorMixer";
import GameManager from "../GameManager";
import GameUtils, { UnitInfo } from "./GameUtils";
import LocalUtils, { TweenObject } from "../LocalUtils";
import AntiExtrusionManager from "./AntiExtrusionManager";
import Building from "../GameStuffAndComps/Building";
import Coin from "../GameStuffAndComps/Coin";
import Enemy from "../GameStuffAndComps/Enemy";
import FloorUI from "../GameStuffAndComps/FloorUI";
import FloorUnlockArea from "../GameStuffAndComps/FloorUnlockArea";
import GameDirector from "./GameDirector";
import GameStuffManager from "./GameStuffManager";
import GameStuffs from "./GameStuffs";
import GatheringBuilding from "../GameStuffAndComps/GatheringBuilding";
import Hero from "../GameStuffAndComps/Hero";
import PushingStuff from "../GameStuffAndComps/PushingStuff";
import Resource, { ResourceInfo, ResourceState, ResourceType } from "../GameStuffAndComps/Resource";
import StorageArea from "../GameStuffAndComps/StorageArea";
import Stuff from "../GameStuffAndComps/Stuff";
import Tower from "../GameStuffAndComps/Tower";
import Unit from "../GameStuffAndComps/Unit";

const {ccclass, property} = cc._decorator;

@ccclass
export default class GameWorld extends cc.Component {
    @property(cc.Node)
    MiddleNode: cc.Node = null;
    @property(Hero)
    MainHero: Hero = null;

    @property([Building])
    statues: Building[] = [];

    @property([Tower])
    towers: Tower[] = [];

    @property([Building])
    centerBuildings: Building[] = [];
    
    @property([GatheringBuilding])
    gatheringBuildingList: GatheringBuilding[] = [];
    
    @property([FloorUnlockArea])
    floorUnlockAreaList: FloorUnlockArea[] = [];

    @property(StorageArea)
    woodStorageArea: StorageArea = null;
    @property(StorageArea)
    rawMeatDropStorageArea: StorageArea = null;
    @property(StorageArea)
    meatStorageArea: StorageArea = null;
    @property(StorageArea)
    cookStorageArea: StorageArea = null;
    @property(StorageArea)
    foodStorageArea: StorageArea = null;

    @property(StorageArea)
    bowStorageArea: StorageArea = null;
    @property(StorageArea)
    bowStorageArea2: StorageArea = null;
    @property([StorageArea])
    conveyerStorageAreas: StorageArea[] = [];

    @property(StorageArea)
    buyFoodStorageArea: StorageArea = null;
    @property(StorageArea)
    coinStorageArea: StorageArea = null;

    @property(StorageArea)
    blockClodStorageArea: StorageArea = null;
    @property(StorageArea)
    blockWoodStorageArea: StorageArea = null;
    @property(StorageArea)
    blockGoldStorageArea: StorageArea = null;
    

    @property(FloorUI)
    FloorUIBuyFood: FloorUI = null;

    @property(cc.Node)
    bucketFloorNode: cc.Node = null

    @property([cc.Node])
    bgNodes: cc.Node[] = [];
    @property([cc.Node])
    expandNodes_1: cc.Node[] = [];
    @property([cc.Node])
    expandNodes_2: cc.Node[] = [];
    @property([cc.Node])
    removeNodes_1: cc.Node[] = [];

    @property([cc.Node])
    bedNodes: cc.Node[] = [];

    @property([cc.Node])
    conveyerNodes: cc.Node[] = [];
    
    @property(Building)
    building_grill: Building = null;
    @property(Building)
    building_meatTable: Building = null;
    @property(Building)
    building_mineEntrance: Building = null;
    @property(GatheringBuilding)
    gatheringBuilding_mine: GatheringBuilding = null;

    @property(cc.Node)
    bowTableNode: cc.Node = null;

    @property(cc.PhysicsPolygonCollider)
    trigger_1: cc.PhysicsPolygonCollider = null;
    @property(cc.PhysicsPolygonCollider)
    trigger_2: cc.PhysicsPolygonCollider = null;

    @property([cc.Prefab])
    heroPrefabs: cc.Prefab[] = [];

    @property([cc.Prefab])
    bulletPrefabs: cc.Prefab[] = [];
    @property([cc.Prefab])
    blockBulletPrefabs: cc.Prefab[] = [];

    @property([cc.Prefab])
    resourcePrefabs: cc.Prefab[] = [];

    @property([cc.Prefab])
    pushingStuffPrefabs: cc.Prefab[] = [];
    
    @property(cc.Prefab)
    driftingMeatPrefab: cc.Prefab = null;
    @property(cc.Prefab)
    coinPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    stockadePrefab: cc.Prefab = null;
    @property(cc.Prefab)
    stockadeLightPrefab: cc.Prefab = null;

    @property(cc.Prefab)
    treePrefab1: cc.Prefab = null;
    @property(cc.Prefab)
    treePrefab2: cc.Prefab = null;

    @property(cc.Node)
    whiteLight: cc.Node = null;

    @property(cc.Node)
    worldBlackMask: cc.Node = null;

    @property(cc.Node)
    stockadeLightParent: cc.Node = null;
    @property(cc.Node)
    frontStuffParent: cc.Node = null;
    @property(cc.Node)
    floatStuffParent: cc.Node = null;


    @property(cc.Material)
    colorMixerMaterial: cc.Material = null;

    get gameStuffs(): GameStuffs {
        return GameUtils.rootGameStuffs;
    }

    private _gameUpdateCallbackId2 = -1;
    private _gameUpdateCallbackId3 = -1;
    private _gameUpdateCallbackId4 = -1;

    enemyList: Enemy[] = [];
    
    curFrameUnits: Unit[] = [];

    Stuffs: Stuff[] = [];

    coinList: Coin[] = [];
    coinResourceList: ResourceInfo[] = [];

    // stockadeLocPoints: cc.Node[] = [];
    // stockadeGroupsIndexes: number[][] = [];
    stockadesInWater: Building[] = [];
    stockades: Building[] = [];
    stockadeLights: cc.Node[] = [];
    // nowAreaRef = 1;
    // isNowAreaUnlockList: boolean[] = [];
    isTheLastAreaUnlocked = false;
    // isAreaUnlocking = false;
    
    treeLocPoints: cc.Node[] = [];
    isTreeLocPointUsed: boolean[] = [];
    lastCutTreeLocPointIndex = -1;

    // gate_1_l: Building = null;
    // gate_1_r: Building = null;
    // gate_1_l_Spine: sp.Skeleton = null;
    // gate_1_r_Spine: sp.Skeleton = null;


    // LIFE-CYCLE CALLBACKS:

    onLoad () {
        GameUtils.rootGameWorld = this;
        this.InitOnLoad();
    }

    InitOnLoad() {
        this.InitGameWorldOnLoad();
        this.InitUnits();
    }

    start () {
        this.InitGameWorld();
        GameUtils.rootGameStuffs.Init();
        GameManager.instance.LateFrameCall(()=>{

            this._gameUpdateCallbackId2 = GameManager.instance.AddGameUpdate('GameWorld-Unit2', (dt: number) =>{
                this.unitGameUpdate2(dt);
            }, false, 2061);
            this._gameUpdateCallbackId3 = GameManager.instance.AddGameUpdate('GameWorld-Unit3', (dt: number) =>{
                this.unitGameUpdate3(dt);
            }, false, 2120);
            this._gameUpdateCallbackId4 = GameManager.instance.AddGameUpdate('GameWorld-Unit4', (dt: number) =>{
                this.unitGameUpdate4(dt);
            }, false, 2210);

            GameManager.instance.AddGameUpdate('GameWorld-Physics', (dt: number)=>{
                cc.director.getPhysicsManager().update();
            }, false, 2200);
            GameManager.instance.AddGameUpdate('GameWorld', (dt: number)=>{
                this.gameUpdate(dt);
            }, false, 4000);
        });

        this.ReSortStuffs();
        // this.whiteLight.opacity = 0;
        this.worldBlackMask.opacity = 0;
            
        // LocalUtils.PlaySound('level_up');
        // GameUtils.rootGameWorld.whiteLight.opacity = 180;
        // GameUtils.rootGameWorldUI.PlayTP(cc.v2(-1500, 1520));

    }

    update (dt: number) {
        if(this.MainHero && this.MainHero.isValid && this.MainHero.node.isValid) {
        } else {
            GameDirector.instance.HeroDeadGameOver();
        }
        // if(!GameUtils.isGameOver) {
        //     let unlockStockadeNum = 0;
        //     let allStockadeNum = this.stockades.length;
        //     this.stockades.forEach((e)=>{
        //         if(e.isStockadeUnlock) {
        //             unlockStockadeNum += 1;
        //         }
        //     });
        //     let leftStockadeNum = allStockadeNum - unlockStockadeNum;
        //     if(leftStockadeNum <= 6) {
        //         GameUtils.GameEnd();
        //         GameUtils.rootCameraControl.MoveToPos(cc.v2(170, -450));
        //         GameManager.instance.scheduleOnce(()=>{
        //             GameUtils.rootCameraControl.PlayCameraZoomScaleTo(0.6, 0.3);
        //         }, 0.2);
        //     }
        //     // console.log(`剩余：${leftStockadeNum}`);
        // }
        if(!GameDirector.instance.isEnemyFollowHero) {
            let heroPos = this.MainHero.rootPosition;
            let b = -2392;
            let k = -0.552;
            if(heroPos.y > (heroPos.x * k + b) ) {
                GameDirector.instance.isEnemyFollowHero = true;
            } else {
            }
        }
    }

    gameUpdate(dt: number) {
        // if(!this.isAreaUnlocking) {
        //     this.CheckNowAreaUnlock();
        // }

        this.ReSortStuffs();
        this.ReSortStuffShowAndHide();
        
        // if(!GameDirector.instance.isAllWaterStockadeUnlock) {
        //     this.CheckAllWaterStockadeUnlock();
        // }
    }

    unitGameUpdate2(dt: number) {
        let units: Unit[] = [];
        this.MiddleNode.children.forEach((e)=>{
            let unit = e.getComponent(Unit);
            if(e.active && unit) {
                units.push(unit);
            }
        });
        this.curFrameUnits = units;
        this.curFrameUnits.forEach((e)=>{
            if(e.tag != 'hero' && e.tag != 'driftingMeat') {
                let rb: cc.RigidBody = e.rbNode.getComponent(cc.RigidBody);
                if(rb) {
                    rb.type = cc.RigidBodyType.Dynamic;
                }
                if(e.isCurFrameRbNodeActive) {
                    e.rbNode.active = true;
                } else {
                    e.rbNode.active = false;
                }
                e.isCurFrameRbNodeActive = false;
            }
        });
    }

    unitGameUpdate3(dt: number) {
        this.curFrameUnits.forEach((e)=>{
            if(e.tag != 'hero' && e.tag != 'driftingMeat') {
                if(e.isCurFrameRbNodeActive2) {
                    e.rbNode.active = true;
                } else {
                    e.rbNode.active = false;
                }
                e.isCurFrameRbNodeActive2 = false;
            }
        });
    }

    unitGameUpdate4(dt: number) {
        this.curFrameUnits.forEach((e)=>{
            if(e.tag != 'hero' && e.tag != 'driftingMeat') {
                e.rbNode.active = false;
            }
        });
        this.curFrameUnits.forEach((e)=>{
            e.SetFinalPos(dt);
        });
        AntiExtrusionManager.instance.Update();
    }

    InitGameWorldOnLoad() {
        // 处理栅栏
        this.MiddleNode.children.forEach((e)=>{
            let building = e.getComponent(Building);
            if(e.active && building && (building.statueRef == 101 || building.statueRef == 102 || building.statueRef == 103)) {
                this.stockades.push(building);
                GameManager.instance.LateFrameCall(()=>{
                    // 生成栅栏血条 ui
                    GameUtils.rootGameWorldUI.GenerateNewStockadeHPBar(building);
                });
                building.isStockadeUnlock = true;
                // building.isStockadeUnlock = false;
                // e.active = false;
                
            // } else if(e.name == 'gate_1_l') {
            //     this.gate_1_l = e.getComponent(Building);
            //     this.gate_1_l_Spine = e.getChildByName('spine').getComponent(sp.Skeleton);
            // } else if(e.name == 'gate_1_r') {
            //     this.gate_1_r = e.getComponent(Building);
            //     this.gate_1_r_Spine = e.getChildByName('spine').getComponent(sp.Skeleton);
            }
        });

        // 隐藏在场景中显示的节点
        this.conveyerNodes.forEach((e)=>{
            e.active = false;
        });
        this.centerBuildings.forEach((e, i)=>{
            if(i == 0) return;
            e.node.active = false;
        });

    }

    InitGameWorld() {
        // 根据路径点生成建筑
        // GameUtils.rootPathPoints.stockadeLocPathPointParentList[0].children.forEach((e)=>{
        //     let node = LocalUtils.GenerateNode(this.MiddleNode, this.stockadePrefab, e.position);
        //     let script = node.getComponent(Building);
        //     if(script) {
        //         script.Init(true);
        //         this.stockadesInWater.push(script);
        //     }
        // });
        // GameUtils.rootPathPoints.stockadeLocPathPointParentList[1].children.forEach((e, i)=>{
        //     let stockade = this.CreateAStockade(e, 1);
        //     if(stockade) {
        //         this.stockades.push(stockade);
        //         if(i > 20 && i <= 36 || i > 44 && i <= 53 || i > 58 && i <= 63) {
        //             stockade.unlockProgress = stockade.unlockMaxProgress;
        //             stockade.OnUnlock();
        //         }
        //         stockade.isStockadeActive = true;
        //         stockade.stockadeIndexInList = i;
        //     }
        // });
        // GameUtils.rootPathPoints.stockadeLocPathPointParentList[2].children.forEach((e)=>{
        //     let stockade = this.CreateAStockade(e, 2);
        //     stockade && this.stockades.push(stockade);
        // });
        // 生成树
        // GameUtils.rootPathPoints.treeLocPathPoints.forEach((e)=>{
        //     this.treeLocPoints.push(e);
        // });
        // this.treeLocPoints.forEach((e, i)=>{
        //     let script = this.CreateATree(cc.v2(e.position));
        //     if(script) {
        //         this.gatheringBuildingList.push(script);
        //         script.treeLocPointIndex = i;
        //         this.isTreeLocPointUsed[i] = true;
        //     }
        // });

        // 隐藏未解锁物体
        this.expandNodes_1.forEach((e)=>{
            e.active = false;
        });
        this.expandNodes_2.forEach((e)=>{
            e.active = false;
        });

        // this.towers.forEach((e)=>{
        //     e.Init();
        //     e.OnUnlock();
        // });
        
        // this.CreatePushingStuff(cc.v2(GameUtils.rootPathPoints.stripLogPathPoint.position), 1);
    }

    InitUnits() {
        let heroInfo = new UnitInfo(this.MainHero.node);
        heroInfo.script = this.MainHero;
        this.MainHero.info = heroInfo;
        GameStuffManager.instance.mainHero = heroInfo;
        GameUtils.heroList.push(heroInfo);

        let enemyInfos = [];
        this.enemyList.forEach((e)=>{
            let enemyInfo = new UnitInfo(e.node);
            enemyInfo.script = e;
            enemyInfo.script.info = enemyInfo;
            enemyInfos.push(enemyInfo);
            if(e.enemyRef == 7) {
                GameStuffManager.instance.mainBoss = enemyInfo;
            }
        });
        
        GameStuffManager.instance.enemyList = enemyInfos;
    }

    // CheckAllWaterStockadeUnlock() {
    //     let isAllUnlock = true;
    //     this.stockadesInWater.forEach((e)=>{
    //         if(!e.isStockadeUnlock) {
    //             isAllUnlock = false;
    //         }
    //     });
    //     if(isAllUnlock) {
    //         GameDirector.instance.OnAllWaterStockadeUnlock();
    //     }
    // }

    UnlockNewHero(index: number, pos: cc.Vec2) {
        if(this.heroPrefabs[index]) {
            let node = LocalUtils.GenerateNode(this.MiddleNode, this.heroPrefabs[index], cc.v3());
            let heroInfo = new UnitInfo(node);
            let script = node.getComponent(Hero);
            
            heroInfo.script = script;
            script.info = heroInfo;
            // cc.tween(this.worldBlackMask).to(0.3, {opacity: 150}).call(()=>{
            // }).start();
            script.OnBorn(()=>{
                // cc.Tween.stopAllByTarget(this.worldBlackMask);
                // cc.tween(this.worldBlackMask).to(0.3, {opacity: 0}).call(()=>{
                //     node.parent = this.MiddleNode;
                // }).start();
            });
            GameManager.instance.LateFrameCall(()=>{
                script.TP(pos);
            });

            GameStuffManager.instance.heroList.push(heroInfo);
            script.heroIndex = GameUtils.heroList.findIndex((e)=>{ return e.script.stuff_id == heroInfo.script.stuff_id; });
            return true;
        }
        return false;
    }

    UnlockTower(towerIndex: number) {
        this.towers[towerIndex].Unlock();
        GameUtils.rootGameWorldUI.GenerateTowerDemandBubble(this.towers[towerIndex].rootPosition.add(cc.v2(0, 350)), towerIndex);
        GameDirector.instance.OnTowerUnlock(towerIndex);
    }

    UnlockExpand(expandIndex: number) {
        let displayStockades: Building[] = [];
        let hideStockades: Building[] = [];
        let displayNodes: cc.Node[] = [];
        let hideNodes: cc.Node[] = [];
        switch(expandIndex) {
            case 0:
                displayStockades = this.stockades.filter((e)=>{
                    return e.node.name == 'stockade_3_expand';
                });
                hideStockades = this.stockades.filter((e)=>{
                    return e.node.name == 'stockade_3_remove';
                });
                displayNodes = this.expandNodes_1;
                break;
            case 1:
                displayStockades = this.stockades.filter((e)=>{
                    return e.node.name == 'stockade_3_expand_2';
                });
                hideStockades = this.stockades.filter((e)=>{
                    return e.node.name == 'stockade_3_remove_2';
                });
                displayNodes = this.expandNodes_2;
                break;
            default:
                break;
        }
        let curDelayTime = 0;
        displayStockades.forEach((e)=>{
            e.isStockadeUnlock = true;
            e.node.active = true;
            curDelayTime += 0.03;
            if(e.stockadeNode) { e.stockadeNode.opacity = 0; } else if(e.img) { e.img.node.opacity = 0; } else { e.node.opacity = 0; }
            GameManager.instance.LateTimeCallOnce(()=>{
                // console.log('解锁！');
                if(e.stockadeNode) { e.stockadeNode.opacity = 255; } else if(e.img) { e.img.node.opacity = 255; } else { e.node.opacity = 255; }
                e.PlayColorFlash(cc.Color.WHITE, 0.4);
            }, 0 + curDelayTime);
        });

        hideStockades.forEach((e)=>{
            e.isStockadeUnlock = false;
            e.PlayColorFlash(cc.Color.WHITE, 1);
            cc.tween(e.stockadeNode).delay(0.2).to(0.2, {opacity: 0}).call(()=>{
                e.node.active = false;
            }).start();
        });

        displayNodes.forEach((e, i)=>{
            GameManager.instance.LateTimeCallOnce(()=>{
                e.active = true;
                let building = e.getComponent(Building);
                if(building) {
                    building.OnUnlock();
                } else {
                    let srcOpacity = e.opacity;
                    e.opacity = 0;
                    cc.tween(e).to(0.3, {opacity: srcOpacity}).start();
                }
            }, 0.6 + i * 0.1);
        });

        hideNodes.forEach((e)=>{
            GameManager.instance.LateTimeCallOnce(()=>{
                e.active = false;
            }, 0.6);
        });

        GameDirector.instance.OnExpandUnlock(expandIndex);
    }

    UnlockExpand1() {
        let curDelayTime = 0.3;
        // 去除中间栅栏
        this.stockades.forEach((e)=>{
            if(e.stockadeGroupIndex == 2) {
                e.isCanUnlock = false;
                e.isStockadeUnlock = false;
                e.PlayColorFlash(cc.Color.WHITE, 1);
                cc.tween(e.baseNode).delay(0.2).to(0.2, {opacity: 0}).call(()=>{
                    e.node.active = false;
                }).start();
            }
        });
        // 生成新的栅栏
        GameUtils.rootPathPoints.stockadeLocPathPointParentList[3].children.forEach((e)=>{
            let stockade = this.CreateAStockade(e, 3);
            if(!stockade) { return; }
            this.stockades.push(stockade);
            stockade.isStockadeActive = true;
            if(stockade.baseNode) { stockade.baseNode.opacity = 0; } else { stockade.node.opacity = 0; }
            if(stockade.shadowNode) { stockade.shadowNode.opacity = 0; }
            curDelayTime += 0.03;
            GameManager.instance.LateTimeCallOnce(()=>{
                // console.log('解锁！');
                stockade.isCanUnlock = true;
                stockade.baseNode.opacity = 255;
                stockade.shadowNode.opacity = 255;
                stockade.PlayColorFlash(cc.Color.WHITE, 0.4);
            }, 0 + curDelayTime);
        });
        GameUtils.rootPathPoints.stockadeLocPathPointParentList[4].children.forEach((e)=>{
            let stockade = this.CreateAStockade(e, 4);
            if(!stockade) { return; }
            this.stockades.push(stockade);
            if(stockade.baseNode) { stockade.baseNode.opacity = 0; } else { stockade.node.opacity = 0; }
            if(stockade.shadowNode) { stockade.shadowNode.opacity = 0; }
            curDelayTime += 0.03;
            GameManager.instance.LateTimeCallOnce(()=>{
                // console.log('解锁！');
                stockade.baseNode.opacity = 255;
                stockade.shadowNode.opacity = 255;
                stockade.PlayColorFlash(cc.Color.WHITE, 0.4);
            }, 0 + curDelayTime);
        });
        this.bgNodes[1].active = true;
        this.bgNodes[1].opacity = 0;
        cc.tween(this.bgNodes[1]).to(0.2, {opacity: 255}).call(()=>{
        }).start();

        GameDirector.instance.OnExpand1Unlock();
    }

    UnlockExpand2() {
        let curDelayTime = 0;
        // 去除中间栅栏
        this.stockades.forEach((e)=>{
            if(e.stockadeGroupIndex == 4) {
                e.isStockadeUnlock = false;
                e.PlayColorFlash(cc.Color.WHITE, 1);
                cc.tween(e.baseNode).delay(0.2).to(0.2, {opacity: 0}).call(()=>{
                    e.node.active = false;
                }).start();
            }
        });
        // 生成新的栅栏
        GameUtils.rootPathPoints.stockadeLocPathPointParentList[5].children.forEach((e)=>{
            let stockade = this.CreateAStockade(e, 5);
            if(!stockade) { return; }
            this.stockades.push(stockade);
            stockade.isStockadeActive = true;
            if(stockade.baseNode) { stockade.baseNode.opacity = 0; } else { stockade.node.opacity = 0; }
            if(stockade.shadowNode) { stockade.shadowNode.opacity = 0; }
            curDelayTime += 0.03;
            GameManager.instance.LateTimeCallOnce(()=>{
                // console.log('解锁！');
                stockade.isCanUnlock = true;
                stockade.baseNode.opacity = 255;
                stockade.shadowNode.opacity = 255;
                stockade.PlayColorFlash(cc.Color.WHITE, 0.4);
            }, 0 + curDelayTime);
        });
        this.bgNodes[2].active = true;
        this.bgNodes[2].opacity = 0;
        cc.tween(this.bgNodes[2]).to(0.2, {opacity: 255}).call(()=>{
        }).start();

        GameManager.instance.LateTimeCallOnce(()=>{
            GameUtils.rootGameStuffs.building_board_1.active = true;
            GameUtils.rootGameStuffs.table_wood.active = true;
        }, 0.4);

        GameDirector.instance.OnExpand2Unlock();
    }

    UnlockWallExpand(index: number) {
        if(index < 0 || index >= 4) {
            return;
        }
        let numStr = '' + (index + 1);
        let curDelayTimeIndex = 0;
        let curDelayTime = 0;
        this.stockades.forEach((e)=>{
            let isUnlock = false;
            if(e.node.name == 'stockade_expand_' + numStr || e.node.name == 'stockade_gateWall_' + numStr || e.node.name == 'gate_' + numStr
                  || e.node.name == 'gate_' + numStr + '_b' || e.node.name == 'gate_' + numStr + '_f') {
                isUnlock = true;
                e.isStockadeUnlock = true;
                e.node.active = true;
                if(e.stockadeNode) { e.stockadeNode.opacity = 0; } else if(e.img) { e.img.node.opacity = 0; } else { e.node.opacity = 0; }
            }
            if(isUnlock) {
                curDelayTimeIndex += 1;
                curDelayTime = curDelayTimeIndex > 5 ? (curDelayTimeIndex - 6) * 0.03 : (5 - curDelayTimeIndex) * 0.03;
                GameManager.instance.LateTimeCallOnce(()=>{
                    // console.log('解锁！');
                    if(e.stockadeNode) {
                        e.stockadeNode.opacity = 255;
                    } else if(e.img) {
                        e.img.node.opacity = 255;
                    } else {
                        e.node.opacity = 255;
                    }
                    e.PlayColorFlash(cc.Color.WHITE, 0.4);
                }, 0 + curDelayTime);
            }
        });
        switch(index) {
            case 0:
                break;
            case 1:
                break;
            case 2:
                break;
            case 3:
                break;
            default:
                break;
        }
    }

    UnlockCenterBuilding(index: number) {
        GameManager.instance.LateTimeCallOnce(()=>{
            let building = this.centerBuildings[index];
            building.node.active = true;
            building.OnUnlock();
            // this.bowTableNode.active = true;
            // this.bowTableNode.scale = 0.3;
            // cc.tween(this.bowTableNode).to(0.3, {scale: 1}).start();
        }, 0.2);
    }

    UnlockConveyer(conveyerIndex: number) {
        // GameManager.instance.LateTimeCallOnce(()=>{
        //     cc.tween(this.bowTableNode).to(0.3, {scale: 0.3}).call(()=>{
        //         this.bowTableNode.active = false;
        //     }).start();
        // }, 0.4);
        GameManager.instance.LateTimeCallOnce(()=>{
            this.conveyerNodes[conveyerIndex].active = true;
        }, 0.2);
    }

    ReSortStuffs() {
        let childs = this.MiddleNode.children;

        this.Stuffs = [];
        childs.forEach((e)=>{
            let stuff = e.getComponent(Stuff);
            if(e.isValid && stuff) {
                this.Stuffs.push(stuff);
                stuff.nodeSortIndex = this.Stuffs.length - 1;
            }
        });

        this.Stuffs.sort((a, b)=>{
            return b.rootPosition.y - a.rootPosition.y;
        });
        // resortNecessaryStuffIndexs.forEach((index)=>{
        //     this.TryReSortSelf(this.Stuffs[index]);
        // });

        // 重写排序算法
        // let resortNecessaryStuffs: Stuff[] = [];
        // this.Stuffs.forEach((e, i)=>{
        //     if(e.isNodeResortNecessary) {
        //         resortNecessaryStuffs.push(e);
        //     }
        // });
        // // resortNecessaryStuffs.sort((a, b) => (b.rootPosition.y - a.rootPosition.y));
        // resortNecessaryStuffs.forEach((stuff)=>{
        //     let index = this.Stuffs.findIndex((e)=>{ return e == stuff });
        //     if(index == -1) console.error('!');
            
        //     let insertIndexAdd = this.SortSearch(this.Stuffs, stuff, index, 0);
        //     this.Stuffs.splice(index, 1);
        //     this.Stuffs.splice(index + insertIndexAdd, 0, stuff);
        // });

        this.Stuffs.forEach((e, index)=>{
            e.node.setSiblingIndex(index);
            // this.MiddleNode.insertChild(e.node, index);
            e.nodeSortIndex = index;
            e.isNodeResortNecessary = false;
        });
    }

    SortSearch(stuffs: Stuff[], stuff: Stuff, index: number, indexAdd: number): number {
        if(indexAdd == 0) {
            let isSerchUp = false;
            if(index - 1 >= 0) {
                let rtn = this.SortSearch(stuffs, stuff, index, -1);
                if(rtn != 0) {
                    isSerchUp = true;
                    return rtn;
                }
            }
            if(!isSerchUp && index + 1 < stuffs.length) {
                return this.SortSearch(stuffs, stuff, index, 1);
            }
        } else if(indexAdd < 0) {
            if(index + indexAdd >= 0 && stuff.rootPosition.y > stuffs[index + indexAdd].rootPosition.y) {
                return this.SortSearch(stuffs, stuff, index, indexAdd - 1) - 1;
            }
        } else if(indexAdd > 0) {
            if(index + indexAdd < stuffs.length && stuff.rootPosition.y < stuffs[index + indexAdd].rootPosition.y) {
                return this.SortSearch(stuffs, stuff, index, indexAdd + 1) + 1;
            }
        }
        return 0;
    }

    ReSortStuffShowAndHide() {
        let diffX = 600;
        let diffY = 800;
        // let diffX = -1000;
        // let diffY = -800;
        let cameraPos = GameUtils.rootCameraControl.camera.node.position;
        let gameWorldRectSize = GameUtils.gameWorldRectSize;
        this.Stuffs.forEach((e, index)=>{
            // e.node.active = true;
            e.node.opacity = 255;
            if(e.tag == 'hero' || e.tag == 'storageArea' || e.tag == 'resourceGroup' || e.tag == 'resource' || e.node.name == 'building_center' || e.node.name == 'guideToStatue') {
                return;
            }
            if(e.rootPosition.x < cameraPos.x - gameWorldRectSize.width/2 - diffX || e.rootPosition.x > cameraPos.x + gameWorldRectSize.width/2 + diffX) {
                // e.node.active = false;
                e.node.opacity = 0;
            }
            if(e.rootPosition.y < cameraPos.y - gameWorldRectSize.height/2 - diffY || e.rootPosition.y > cameraPos.y + gameWorldRectSize.height/2 + diffY) {
                // e.node.active = false;
                e.node.opacity = 0;
            }
        });
    }

    // TryReSortSelf(stuff: Stuff) {
    //     let depth = -1;
    //     let move = 0;
    //     let srcIndex = stuff.nodeSortIndex;

    //     if(srcIndex - 1 >= 0 && stuff.rootPosition.y > this.Stuffs[srcIndex - 1].rootPosition.y) {
    //         stuff.nodeSortIndex = srcIndex - 1;
    //         this.Stuffs[srcIndex - 1].nodeSortIndex = srcIndex;
    //         depth = this.TryReSortUpSelf(stuff, srcIndex, -1);
    //     }
    //     if(depth == -1 && srcIndex + 1 < this.Stuffs.length && stuff.rootPosition.y < this.Stuffs[srcIndex + 1].rootPosition.y) {
    //         stuff.nodeSortIndex = srcIndex - 1;
    //         this.Stuffs[srcIndex + 1].nodeSortIndex = srcIndex;
    //         depth = this.TryReSortDownSelf(stuff, srcIndex, 1);
    //         if(depth > -1) {
    //             console.log(`${stuff.node.name} ====> 向下移动 ${depth} 次`);
    //             move = depth;
    //         }
    //     } else {
    //         if(depth > -1) {
    //             console.log(`${stuff.node.name} ====> 向上移动 ${depth} 次`);
    //             move = -depth;
    //         }
    //     }

    //     if(depth > -1) {
    //         this.Stuffs.splice(srcIndex, 1);
    //         this.Stuffs.splice(srcIndex + move, 0, stuff);
    //     }
    // }

    // TryReSortUpSelf(stuff: Stuff, srcIndex: number, indexAdd: number) {
    //     if(srcIndex + indexAdd >= 0) {
    //         if(stuff.rootPosition.y > this.Stuffs[srcIndex + indexAdd].rootPosition.y) {
    //             stuff.nodeSortIndex -= 1;
    //             this.Stuffs[srcIndex + indexAdd].nodeSortIndex = srcIndex + indexAdd + 1;
    //             let depth = this.TryReSortUpSelf(stuff, srcIndex, indexAdd - 1);
    //             return depth + 1;
    //         }
    //     }
    //     return 1;
    // }

    // TryReSortDownSelf(stuff: Stuff, srcIndex: number, indexAdd: number) {
    //     if(srcIndex + indexAdd + 1 < this.Stuffs.length) {
    //         if(stuff.rootPosition.y < this.Stuffs[srcIndex + indexAdd].rootPosition.y) {
    //             stuff.nodeSortIndex += 1;
    //             this.Stuffs[srcIndex + indexAdd].nodeSortIndex = srcIndex + indexAdd - 1;
    //             let depth = this.TryReSortDownSelf(stuff, srcIndex, indexAdd + 1);
    //             return depth + 1;
    //         }
    //     }
        
    //     return 1;
    // }

    CreateAStockade(stockadeLocPoint: cc.Node, stockadeGroupIndex: number) {
        let node = LocalUtils.GenerateNode(this.MiddleNode, this.stockadePrefab, stockadeLocPoint.position);
        let script = node.getComponent(Building);
        if(script) {
            script.Init();
            script.isCanUnlock = false;
            script.stockadeGroupIndex = stockadeGroupIndex;
            return script;
        }
        // let lightNode = LocalUtils.GenerateNode(this.stockadeLightParent, this.stockadeLightPrefab, e.position);
        // this.stockadeLights.push(lightNode);

        // 发光
        // cc.tween(new TweenObject(0, (value: number)=>{
        //     this.stockadeLights.forEach((e)=>{
        //         e.getChildByName('center').opacity = 255 * value;
        //     });
        // })).set({value: 0}).to(0.6, {value: 1}).delay(0.2).to(0.6, {value: 0}).union().repeatForever().start();
    }

    CreateATree(pos: cc.Vec2) {
        let randomResult = Math.random() > 0.65 ? 1 : 0;
        let node = LocalUtils.GenerateNode(this.MiddleNode, (randomResult == 0) ? this.treePrefab1 : this.treePrefab2, cc.v3(pos));
        let script = node.getComponent(GatheringBuilding);
        if(script) {
            script.Init(true);
        }
        return script;
    }

    TryPickUpCoins(pos: cc.Vec2, radius: number) {
        let coins: Coin[] = [];
        this.coinList.forEach((e)=>{
            let distance = e.rootPosition.sub(pos).len();
            if(e.isCanPick && distance < radius) {
                coins.push(e);
            }
        });
        coins.forEach((e)=>{
            e.SetOnPickUpCallback(()=>{
                GameUtils.AddCoin(1);
            });
            e.SetOnDestroyCallback(()=>{
                this.RemoveCoinFromList(e);
            });
            e.FlyToMainHero();
        });
        return coins;
    }

    RemoveCoinFromList(coin: Coin) {
        this.coinList.splice(this.coinList.findIndex((e)=>{ return e == coin; }), 1);
    }

    CreateCoin(pos: cc.Vec2, num = 1) {
        // let node = LocalUtils.GenerateNode(this.MiddleNode, this.coinPrefab, cc.v3(pos));
        // let script = node.getComponent(Coin);
        // if(script) {
        //     this.coinList.push(script);
        // }
        
        let stepRadius = 70;
        for(let i = 0; i < num; i++) {
            GameManager.instance.LateTimeCallOnce(()=>{
                let radius = Math.sqrt(i) * stepRadius;
                let randomDir = LocalUtils.AngleToVec2(Util.RandomRange(-180, 180), cc.v2(0, 1));
                let posOffset = randomDir.normalize().mul(radius).scale(cc.v2(1, 0.8));
    
                let node = LocalUtils.GenerateNode(this.MiddleNode, this.coinPrefab, cc.v3(pos));
                let script = node.getComponent(Coin);
                if(script) {
                    GameManager.instance.LateFrameCall(()=>{
                        script.JumpToPosOffset(pos, posOffset);
                    });
                    this.coinList.push(script);
                }
            }, i * 0.02);
        }
    }

    CreatePushingStuff(pos: cc.Vec2, pushingStuffRef: number, isFromHole = false, isFlip = false) {
        let node = LocalUtils.GenerateNode(this.MiddleNode, this.pushingStuffPrefabs[pushingStuffRef - 1], cc.v3(pos));
        let script = node.getComponent(PushingStuff);
        if(script) {
            if(isFlip) {
                script.isFlip = true;
            }
            script.Init(isFromHole);
        }
    }

    RefreshBucketNode(pos: cc.Vec2, angle: number) {
        this.bucketFloorNode.setPosition(pos);
        this.bucketFloorNode.angle = angle;
        let imgsNode = this.bucketFloorNode.getChildByName('imgs');
        let img_bucket_1 = imgsNode.getChildByName('img_bucket_1');
        let img_bucket_2 = imgsNode.getChildByName('img_bucket_2');
        let img_bucket_3 = imgsNode.getChildByName('img_bucket_3');
        let img_bucket_4 = imgsNode.getChildByName('img_bucket_4');
        img_bucket_1.active = false;
        img_bucket_2.active = false;
        img_bucket_3.active = false;
        img_bucket_4.active = false;
        if(angle >= -180 && angle < -122 || angle >= 148 && angle <= 180) {
            // console.log('3');
            img_bucket_3.active = true;
        } else if(angle >= -122 && angle <= -32) {
            // console.log('2');
            img_bucket_2.active = true;
        } else if(angle >= -32 && angle <= 58) {
            // console.log('1');
            img_bucket_1.active = true;
        } else {
            // console.log('4');
            img_bucket_4.active = true;
        }
    }

    PlayUnlockFloorAnim(areaRef: number) {
        let floorNode = this.gameStuffs.floors[areaRef - 1];
        floorNode.active = true;
        if(floorNode.children.length == 0) {
            floorNode.opacity = 0;
            cc.tween(floorNode).to(0.3, {opacity: 255}).start();
            return;
        }
        let cellNodes = floorNode.children[0].children;
        let dir = LocalUtils.AngleToVec2(-15);
        let dir2 = LocalUtils.AngleToVec2(-140);
        let pos0 = cc.v2();
        if(areaRef == 1 || areaRef == 3 || areaRef == 4) {
            dir = LocalUtils.AngleToVec2(-140).neg();
            dir2 = LocalUtils.AngleToVec2(-15);
            pos0 = cc.v2(cellNodes[3].position);
        } else if(areaRef == 2) {
            dir = LocalUtils.AngleToVec2(-40).neg();
            dir2 = LocalUtils.AngleToVec2(15);
            pos0 = cc.v2(cellNodes[3].position);
        } else if(areaRef == 5) {
            dir = LocalUtils.AngleToVec2(130).neg();
            dir2 = LocalUtils.AngleToVec2(-85);
            pos0 = cc.v2(cellNodes[15].position);
        } else if(areaRef == 6 || areaRef == 7) {
            dir = LocalUtils.AngleToVec2(40).neg();
            dir2 = LocalUtils.AngleToVec2(165);
            pos0 = cc.v2(cellNodes[12].position);
        } else if(areaRef == 8) {
            dir = LocalUtils.AngleToVec2(-50).neg();
            dir2 = LocalUtils.AngleToVec2(75);
            pos0 = cc.v2(cellNodes[0].position);
        }
        cellNodes.forEach((e)=>{
            e.active = false;
            let pos = cc.v2(e.position);
            let distance = (pos.x * dir2.y - pos.y * dir2.x + pos0.y * dir2.x - pos0.x * dir.y) / (dir2.x * dir.y - dir2.y * dir.x);
            // console.log(`distance: ${distance}`);
            GameManager.instance.scheduleOnce(()=>{
                e.active = true;
                let colorMixerComp = e.getComponent(ColorMixer);
                if(!colorMixerComp) {
                    colorMixerComp = e.addComponent(ColorMixer);
                    colorMixerComp.Init(GameUtils.rootGameWorld.colorMixerMaterial, cc.Color.WHITE, 0.5);
                    colorMixerComp.LoadNode();
                }
                colorMixerComp.isSetData = true;
                colorMixerComp.PlayFlashColor(cc.Color.WHITE, 0.6, 0.2);
            }, distance * 0.001);
        });
    }

    GetStockadesGroupUnclokDuringTime(groupRef: number, pos0: cc.Vec2) {
        // let duringTime = GameUtils.Fake3dDistance(GameUtils.mainHero.script.rootPosition, cc.v2(e.position)) / 500;
        let dir: cc.Vec2;
        let b = 527;
        if(groupRef == 1) {
        } else if(groupRef == 2) {
        } else if(groupRef == 3) {
        } else if(groupRef == 4) {
        } else if(groupRef == 5) {
        } else if(groupRef == 6) {
        } else if(groupRef == 7) {
        } else if(groupRef == 8) {
        } else if(groupRef == 9) {
        } else if(groupRef == 10) {
        }
        dir = cc.v2(-1, -0.8).normalize();
        let k = (dir.x > 0 && dir.y > 0 || dir.x < 0 && dir.y < 0) ? -0.552 : 0.552;
        let distance = (pos0.y - b - pos0.x * k) / (dir.y - dir.x * k);
        return distance / 500;
    }

}
